#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找GameView类
"""

def find_gameview():
    """查找GameView类"""
    files_to_search = ['main.py', 'view.py', 'ui.py', 'game_view.py']
    
    for filename in files_to_search:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n搜索文件: {filename}")
            print("-" * 40)
            
            for i, line in enumerate(lines, 1):
                if 'class GameView' in line or 'GameView' in line:
                    print(f"第{i}行: {line.strip()}")
        
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
        except Exception as e:
            print(f"搜索 {filename} 失败: {e}")

if __name__ == "__main__":
    find_gameview()
