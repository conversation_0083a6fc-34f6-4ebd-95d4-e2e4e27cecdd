#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找UI绘制代码
"""

def find_ui_draw():
    """查找UI绘制代码"""
    files_to_search = ['main.py', 'combat_system.py', 'level.py']
    
    for filename in files_to_search:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n搜索文件: {filename}")
            print("-" * 40)
            
            keywords = ['draw', 'blit', 'rect', 'bar', 'UI', 'interface', '绘制', '界面']
            
            for keyword in keywords:
                found_lines = []
                for i, line in enumerate(lines, 1):
                    if keyword in line and ('def ' in line or 'class ' in line):
                        found_lines.append((i, line.strip()))
                
                if found_lines:
                    print(f"\n关键词 '{keyword}' (方法/类):")
                    for line_num, line_content in found_lines:
                        print(f"  第{line_num}行: {line_content}")
        
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
        except Exception as e:
            print(f"搜索 {filename} 失败: {e}")

if __name__ == "__main__":
    find_ui_draw()
