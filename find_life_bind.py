#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找生命缠绕技能
"""

def find_life_bind():
    """查找生命缠绕技能"""
    try:
        with open('combat_system.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索生命缠绕相关代码...")
        
        keywords = ['生命缠绕', 'life_bind', 'wood_wood', 'life_entangle']
        
        for keyword in keywords:
            print(f"\n搜索关键词: '{keyword}'")
            found_lines = []
            
            for i, line in enumerate(lines, 1):
                if keyword in line:
                    found_lines.append((i, line.strip()))
            
            if found_lines:
                print(f"  找到 {len(found_lines)} 处:")
                for line_num, line_content in found_lines:
                    print(f"    第{line_num}行: {line_content}")
            else:
                print(f"  未找到")
        
        # 查找所有组合技能
        print(f"\n搜索所有组合技能:")
        combo_lines = []
        for i, line in enumerate(lines, 1):
            if 'def ' in line and any(combo in line for combo in ['wood', 'water', 'fire', 'earth', 'metal']):
                combo_lines.append((i, line.strip()))
        
        if combo_lines:
            print(f"找到组合技能方法:")
            for line_num, line_content in combo_lines:
                print(f"  第{line_num}行: {line_content}")
        
        return True
        
    except Exception as e:
        print(f"搜索失败: {e}")
        return False

if __name__ == "__main__":
    find_life_bind()
