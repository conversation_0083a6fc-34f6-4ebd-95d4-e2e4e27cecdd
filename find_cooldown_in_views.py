#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
在views.py中查找技能等待条
"""

def find_cooldown_in_views():
    """在views.py中查找技能等待条"""
    try:
        with open('views.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索views.py中的技能等待条...")
        
        keywords = ['等待条', 'cooldown', '技能冷却', 'skill_bar', 'draw_bar', 'progress', '冷却', 'bar']
        
        for keyword in keywords:
            found_lines = []
            for i, line in enumerate(lines, 1):
                if keyword in line:
                    found_lines.append((i, line.strip()))
            
            if found_lines:
                print(f"\n关键词 '{keyword}':")
                for line_num, line_content in found_lines:
                    print(f"  第{line_num}行: {line_content}")
        
        # 查找所有绘制相关的方法
        print(f"\n所有绘制相关的代码:")
        for i, line in enumerate(lines, 1):
            if 'draw' in line.lower() or 'blit' in line or 'rect' in line:
                if i > 200:  # 只显示后面的部分
                    print(f"  第{i}行: {line.strip()}")
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_cooldown_in_views()
