#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找HealingFlower类
"""

def find_healing_flower():
    """查找HealingFlower类"""
    try:
        with open('combat_system.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索HealingFlower类...")
        
        for i, line in enumerate(lines, 1):
            if 'class HealingFlower' in line:
                print(f"找到HealingFlower类在第{i}行")
                
                # 显示类的初始化方法
                start = max(0, i-1)
                end = min(len(lines), i+50)
                print(f"\n类定义内容 (第{start+1}-{end}行):")
                for j in range(start, end):
                    print(f"{j+1:4d}: {lines[j].rstrip()}")
                    if 'class ' in lines[j] and j > i and 'HealingFlower' not in lines[j]:
                        break
                return i
        
        print("未找到HealingFlower类")
        
        # 搜索HealingFlower的引用
        print("\n搜索HealingFlower的引用:")
        for i, line in enumerate(lines, 1):
            if 'HealingFlower' in line:
                print(f"第{i}行: {line.strip()}")
        
        return None
        
    except Exception as e:
        print(f"搜索失败: {e}")
        return None

if __name__ == "__main__":
    find_healing_flower()
