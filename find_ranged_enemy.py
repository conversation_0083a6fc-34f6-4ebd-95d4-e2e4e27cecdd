#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找远程敌人攻击代码
"""

def find_ranged_enemy():
    """查找远程敌人攻击代码"""
    files_to_search = ['level.py', 'enemy.py', 'enemies.py', 'combat_system.py']
    
    for filename in files_to_search:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n搜索文件: {filename}")
            print("-" * 40)
            
            keywords = ['ranged', '远程', 'RangedEnemy', 'attack', '攻击', 'projectile', '发射']
            
            for keyword in keywords:
                found_lines = []
                for i, line in enumerate(lines, 1):
                    if keyword in line:
                        found_lines.append((i, line.strip()))
                
                if found_lines:
                    print(f"\n关键词 '{keyword}':")
                    for line_num, line_content in found_lines[:5]:  # 只显示前5个
                        print(f"  第{line_num}行: {line_content}")
        
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
        except Exception as e:
            print(f"搜索 {filename} 失败: {e}")

if __name__ == "__main__":
    find_ranged_enemy()
