#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将frame-1.png和frame-2.png合成为combo_animation.gif
"""

from PIL import Image
import os

def create_combo_gif():
    """创建组合技能动画GIF"""
    
    # 定义文件路径
    assets_dir = "assets/images"
    frame1_path = os.path.join(assets_dir, "frame-1.png")
    frame2_path = os.path.join(assets_dir, "frame-2.png")
    output_path = os.path.join(assets_dir, "combo_animation.gif")
    
    # 检查输入文件是否存在
    if not os.path.exists(frame1_path):
        print(f"错误：找不到文件 {frame1_path}")
        return False
    
    if not os.path.exists(frame2_path):
        print(f"错误：找不到文件 {frame2_path}")
        return False
    
    try:
        # 加载PNG图片
        print("正在加载PNG图片...")
        frame1 = Image.open(frame1_path)
        frame2 = Image.open(frame2_path)
        
        # 确保两个图片尺寸一致
        if frame1.size != frame2.size:
            print(f"警告：图片尺寸不一致")
            print(f"Frame 1: {frame1.size}")
            print(f"Frame 2: {frame2.size}")
            
            # 获取最大尺寸
            max_width = max(frame1.width, frame2.width)
            max_height = max(frame1.height, frame2.height)
            
            # 调整图片尺寸（保持透明背景）
            if frame1.mode != 'RGBA':
                frame1 = frame1.convert('RGBA')
            if frame2.mode != 'RGBA':
                frame2 = frame2.convert('RGBA')
            
            # 创建新的图片并居中粘贴
            new_frame1 = Image.new('RGBA', (max_width, max_height), (0, 0, 0, 0))
            new_frame2 = Image.new('RGBA', (max_width, max_height), (0, 0, 0, 0))
            
            # 计算居中位置
            x1 = (max_width - frame1.width) // 2
            y1 = (max_height - frame1.height) // 2
            x2 = (max_width - frame2.width) // 2
            y2 = (max_height - frame2.height) // 2
            
            new_frame1.paste(frame1, (x1, y1), frame1)
            new_frame2.paste(frame2, (x2, y2), frame2)
            
            frame1 = new_frame1
            frame2 = new_frame2
        
        # 确保图片为RGBA模式以支持透明度
        if frame1.mode != 'RGBA':
            frame1 = frame1.convert('RGBA')
        if frame2.mode != 'RGBA':
            frame2 = frame2.convert('RGBA')
        
        # 创建帧列表
        frames = [frame1, frame2]
        
        print("正在创建GIF动画...")
        
        # 保存为GIF
        # duration: 每帧持续时间（毫秒）
        # loop: 0表示无限循环
        # disposal: 2表示恢复到背景色（对透明度处理更好）
        frame1.save(
            output_path,
            save_all=True,
            append_images=[frame2],
            duration=500,  # 每帧0.5秒
            loop=0,        # 无限循环
            disposal=2,    # 恢复到背景色
            transparency=0,  # 设置透明色索引
            optimize=True   # 优化文件大小
        )
        
        print(f"✅ GIF动画创建成功！")
        print(f"📁 保存位置: {output_path}")
        print(f"📊 文件大小: {os.path.getsize(output_path)} 字节")
        print(f"🎬 动画参数:")
        print(f"   - 帧数: 2")
        print(f"   - 每帧时长: 0.5秒")
        print(f"   - 循环: 无限循环")
        print(f"   - 尺寸: {frame1.size}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建GIF时发生错误: {e}")
        return False

if __name__ == "__main__":
    print("🎮 组合技能动画GIF生成器")
    print("=" * 40)
    
    success = create_combo_gif()
    
    if success:
        print("\n🎉 任务完成！")
    else:
        print("\n💥 任务失败！")
